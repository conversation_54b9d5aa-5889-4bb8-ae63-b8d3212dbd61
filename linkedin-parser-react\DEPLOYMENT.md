﻿# LinkedIn Parser - 部署指南

本文档介绍如何部署 LinkedIn Parser React 应用到各种环境。

## 🚀 快速部署

### 1. 构建生产版本

```bash
# 安装依赖
npm install

# 构建生产版本
npm run build
```

构建完成后，`dist` 目录包含所有静态文件。

### 2. 预览构建结果

```bash
npm run preview
```

## 🌐 部署选项

### Vercel 部署

1. 将代码推送到 GitHub 仓库
2. 在 [Vercel](https://vercel.com) 导入项目
3. 配置构建设置：
   - Build Command: `npm run build`
   - Output Directory: `dist`
   - Install Command: `npm install`

### Netlify 部署

1. 将代码推送到 GitHub 仓库
2. 在 [Netlify](https://netlify.com) 导入项目
3. 配置构建设置：
   - Build command: `npm run build`
   - Publish directory: `dist`

### GitHub Pages 部署

1. 安装 gh-pages：
```bash
npm install --save-dev gh-pages
```

2. 在 `package.json` 添加部署脚本：
```json
{
  "scripts": {
    "deploy": "gh-pages -d dist"
  },
  "homepage": "https://yourusername.github.io/linkedin-parser-react"
}
```

3. 部署：
```bash
npm run build
npm run deploy
```

### Docker 部署

创建 `Dockerfile`：

```dockerfile
# 构建阶段
FROM node:18-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine

COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

创建 `nginx.conf`：

```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        location / {
            try_files $uri $uri/ /index.html;
        }

        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

构建和运行：

```bash
docker build -t linkedin-parser .
docker run -p 80:80 linkedin-parser
```

## ⚡ 性能优化

### 1. 代码分割

应用已配置自动代码分割，大型组件会被分割成独立的 chunk。

### 2. 资源优化

- 图片使用 WebP 格式
- 启用 Gzip 压缩
- 配置适当的缓存策略

### 3. 构建优化

在 `vite.config.ts` 中配置：

```typescript
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          utils: ['./src/utils/index.ts']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  }
});
```

## 🔧 环境配置

### 生产环境变量

创建 `.env.production`：

```env
VITE_APP_TITLE=LinkedIn Parser
VITE_APP_VERSION=1.0.0
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_MINIFY=true
```

### 安全配置

1. **CSP 头部**：
```
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';
```

2. **HTTPS 重定向**：
```nginx
server {
    listen 80;
    return 301 https://$server_name$request_uri;
}
```

## 📊 监控和分析

### 1. 性能监控

使用 Web Vitals 监控：

```typescript
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

getCLS(console.log);
getFID(console.log);
getFCP(console.log);
getLCP(console.log);
getTTFB(console.log);
```

### 2. 错误监控

集成 Sentry 或其他错误监控服务：

```typescript
import * as Sentry from "@sentry/react";

Sentry.init({
  dsn: "YOUR_DSN_HERE",
  environment: process.env.NODE_ENV,
});
```

## 🔄 CI/CD 配置

### GitHub Actions

创建 `.github/workflows/deploy.yml`：

```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run tests
      run: npm test

    - name: Build
      run: npm run build

    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v20
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
        vercel-args: '--prod'
```

## 🛠️ 故障排除

### 常见问题

1. **构建失败**：检查 Node.js 版本（推荐 18+）
2. **路由问题**：确保服务器配置了 SPA 回退
3. **静态资源 404**：检查 base URL 配置

### 调试技巧

1. 启用详细日志：
```bash
npm run build -- --debug
```

2. 分析包大小：
```bash
npm run build -- --analyze
```

## 📝 检查清单

部署前检查：

- [ ] 运行所有测试
- [ ] 检查构建输出
- [ ] 验证环境变量
- [ ] 测试生产构建
- [ ] 配置域名和 SSL
- [ ] 设置监控和日志
- [ ] 备份数据库（如适用）

## 🆘 支持

如果遇到部署问题，请：

1. 检查构建日志
2. 验证环境配置
3. 查看浏览器控制台错误
4. 提交 Issue 到项目仓库
