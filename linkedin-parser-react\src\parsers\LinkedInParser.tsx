﻿/**
 * LinkedIn Main Parser Component
 * Converts parse.py functionality to React - Main entry point for all parsing
 */

import React, { useState, useCallback, useMemo } from 'react';
import { BaseParserProps, ParserConfig, ParseResult } from '@/types';
import { useSearchPageParser } from './SearchPageParser';
import { useCompanyInfoParser } from './CompanyInfoParser';
import { useHumanInfoParser } from './HumanInfoParser';

export type PageType = 'search' | 'company_info' | 'human_info' | 'auto_detect';

interface LinkedInParserProps extends BaseParserProps {
  pageType?: PageType;
  config?: ParserConfig;
}

interface ParseResults {
  search?: ParseResult;
  company?: any;
  human?: any;
  pageType: PageType;
  timestamp: string;
}

/**
 * Main LinkedIn Parser Hook
 */
export function useLinkedInParser() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<ParseResults | null>(null);

  // Initialize individual parsers
  const searchParser = useSearchPageParser();
  const companyParser = useCompanyInfoParser();
  const humanParser = useHumanInfoParser();

  /**
   * Auto-detect page type from HTML content
   */
  const detectPageType = useCallback((htmlContent: string): PageType => {
    const lowerContent = htmlContent.toLowerCase();

    // Check for search page indicators
    if (lowerContent.includes('search-results') ||
        lowerContent.includes('search-global-typeahead') ||
        lowerContent.includes('urn:li:fsd_profile') ||
        lowerContent.includes('search-result')) {
      return 'search';
    }

    // Check for company page indicators
    if (lowerContent.includes('company-employees') ||
        lowerContent.includes('org-top-card') ||
        lowerContent.includes('company-overview') ||
        lowerContent.includes('/company/')) {
      return 'company_info';
    }

    // Check for human profile indicators
    if (lowerContent.includes('profile-top-card') ||
        lowerContent.includes('experience-section') ||
        lowerContent.includes('education-section') ||
        lowerContent.includes('/in/')) {
      return 'human_info';
    }

    // Default to search if can't determine
    return 'search';
  }, []);

  /**
   * Parse content based on detected or specified page type
   */
  const parseContent = useCallback(async (
    htmlContent: string,
    pageType: PageType = 'auto_detect',
    config: ParserConfig = { from_type: 'html', login_status: 'not_logged_in' }
  ): Promise<ParseResults> => {
    setIsLoading(true);
    setError(null);

    try {
      // Auto-detect page type if needed
      const detectedPageType = pageType === 'auto_detect' ? detectPageType(htmlContent) : pageType;

      const result: ParseResults = {
        pageType: detectedPageType,
        timestamp: new Date().toISOString()
      };

      // Parse based on page type
      switch (detectedPageType) {
        case 'search':
          result.search = await searchParser.parseSearchPage(htmlContent, config);
          break;

        case 'company_info':
          result.company = await companyParser.parseCompanyInfo(htmlContent, config);
          break;

        case 'human_info':
          result.human = await humanParser.parseHumanInfo(htmlContent, config);
          break;

        default:
          throw new Error(`Unsupported page type: ${detectedPageType}`);
      }

      setResults(result);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown parsing error';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [detectPageType, searchParser, companyParser, humanParser]);

  /**
   * Parse search page specifically
   */
  const parseSearchPage = useCallback(async (
    htmlContent: string,
    config?: ParserConfig
  ): Promise<ParseResult> => {
    return searchParser.parseSearchPage(htmlContent, config);
  }, [searchParser]);

  /**
   * Parse company info page specifically
   */
  const parseCompanyInfo = useCallback(async (
    htmlContent: string,
    config?: ParserConfig
  ): Promise<any> => {
    return companyParser.parseCompanyInfo(htmlContent, config);
  }, [companyParser]);

  /**
   * Parse human info page specifically
   */
  const parseHumanInfo = useCallback(async (
    htmlContent: string,
    config?: ParserConfig
  ): Promise<any> => {
    return humanParser.parseHumanInfo(htmlContent, config);
  }, [humanParser]);

  /**
   * Get parsing statistics
   */
  const getStats = useMemo(() => {
    if (!results) return null;

    const stats = {
      pageType: results.pageType,
      timestamp: results.timestamp,
      totalItems: 0,
      breakdown: {} as Record<string, number>
    };

    if (results.search) {
      stats.breakdown.people = Object.keys(results.search.people_models).length;
      stats.breakdown.companies = Object.keys(results.search.company_models).length;
      stats.breakdown.posts = Object.keys(results.search.post_models).length;
      stats.totalItems = stats.breakdown.people + stats.breakdown.companies + stats.breakdown.posts;
    } else if (results.company) {
      stats.breakdown.company = 1;
      stats.breakdown.employees = results.company.employees?.length || 0;
      stats.totalItems = 1;
    } else if (results.human) {
      stats.breakdown.human = 1;
      stats.breakdown.workExperience = results.human.work_experience?.length || 0;
      stats.breakdown.education = results.human.educational_experience?.length || 0;
      stats.totalItems = 1;
    }

    return stats;
  }, [results]);

  return {
    parseContent,
    parseSearchPage,
    parseCompanyInfo,
    parseHumanInfo,
    detectPageType,
    isLoading: isLoading || searchParser.isLoading || companyParser.isLoading || humanParser.isLoading,
    error: error || searchParser.error || companyParser.error || humanParser.error,
    results,
    stats: getStats
  };
}

/**
 * LinkedIn Parser Component
 */
export const LinkedInParser: React.FC<LinkedInParserProps> = ({
  htmlContent,
  onParseComplete,
  onParseError,
  pageType = 'auto_detect',
  config,
  className
}) => {
  const { parseContent, isLoading, error, results, stats } = useLinkedInParser();

  const handleParse = useCallback(async () => {
    try {
      const result = await parseContent(htmlContent, pageType, config);
      onParseComplete?.(result);
    } catch (err) {
      const parseError = {
        code: 'LINKEDIN_PARSE_ERROR',
        message: err instanceof Error ? err.message : 'Unknown error',
        details: err
      };
      onParseError?.(parseError);
    }
  }, [htmlContent, pageType, config, parseContent, onParseComplete, onParseError]);

  React.useEffect(() => {
    if (htmlContent) {
      handleParse();
    }
  }, [htmlContent, handleParse]);

  if (isLoading) {
    return (
      <div className={`${className} text-center p-4`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-linkedin-blue mx-auto mb-2"></div>
        <div>解析 LinkedIn 页面中...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${className} text-red-500 p-4 bg-red-50 rounded-lg`}>
        <h3 className="font-semibold mb-2">解析错误</h3>
        <p>{error}</p>
      </div>
    );
  }

  if (results && stats) {
    return (
      <div className={`${className} p-4 bg-green-50 rounded-lg`}>
        <h3 className="font-semibold text-green-800 mb-2">解析完成</h3>
        <div className="text-sm text-green-700">
          <p>页面类型: {stats.pageType}</p>
          <p>解析时间: {new Date(stats.timestamp).toLocaleString()}</p>
          <p>总项目数: {stats.totalItems}</p>
          {Object.entries(stats.breakdown).map(([key, value]) => (
            <p key={key}>{key}: {value}</p>
          ))}
        </div>
      </div>
    );
  }

  return null;
};

/**
 * Export all parser components and hooks
 */
export { useSearchPageParser } from './SearchPageParser';
export { useCompanyInfoParser } from './CompanyInfoParser';
export { useHumanInfoParser } from './HumanInfoParser';
export { SearchPageParser } from './SearchPageParser';
export { CompanyInfoParser } from './CompanyInfoParser';
export { HumanInfoParser } from './HumanInfoParser';
