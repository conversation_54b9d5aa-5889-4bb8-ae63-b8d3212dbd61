﻿/**
 * LinkedIn Human Info Parser Component
 * Converts parse_human_info_page.py functionality to React
 */

import React, { useState, useCallback } from 'react';
import {
  ParseHumanWorkExperience,
  ParseHumanEducationExperience,
  ParseHumanLanguageAbility,
  BaseParserProps,
  ParserConfig
} from '@/types';
import {
  HTMLParser,
  LinkedInPatterns,
  customUnescape,
  safeString,
  normalizeText,
  safeJsonParse
} from '@/utils';

interface HumanInfoParserProps extends BaseParserProps {
  config?: ParserConfig;
}

interface NotLoginHumanData {
  country_code: string;
  human_avatar: string;
  full_name: string;
  linkedin_username: string;
  job_title: string;
  location: string;
  about: string;
  work_experience: ParseHumanWorkExperience[];
  educational_experience: ParseHumanEducationExperience[];
  language_ability: ParseHumanLanguageAbility[];
  [key: string]: any;
}

interface LoginHumanData {
  html_str: string;
  human_name: string;
}

/**
 * Human Info Parser Hook
 */
export function useHumanInfoParser() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Parse human base info from JSON-LD
   */
  const parseHumanBaseInfo = useCallback((parser: HTMLParser): Partial<NotLoginHumanData> => {
    const result: Partial<NotLoginHumanData> = {
      country_code: '',
      human_avatar: ''
    };

    try {
      const jsonLdData = parser.extractJsonLD();

      for (const data of jsonLdData) {
        const graphData = data['@graph'] || [];

        for (const item of graphData) {
          if (item['@type'] === 'Person') {
            result.country_code = item.address?.addressCountry || '';
            result.human_avatar = item.image?.contentUrl || '';
            break;
          }
        }
      }

      return result;
    } catch (error) {
      console.warn('Failed to parse human base info:', error);
      return result;
    }
  }, []);

  /**
   * Parse work experience
   */
  const parseWorkExperience = useCallback((parser: HTMLParser): ParseHumanWorkExperience[] => {
    const experiences: ParseHumanWorkExperience[] = [];

    try {
      const experienceElements = parser.getElements('ul.experience__list li');

      experienceElements.forEach(element => {
        const titleElement = element.querySelector('h3');
        const companyElement = element.querySelector('h4 span[aria-hidden="true"]');
        const companyLinkElement = element.querySelector('h4 a');
        const dateElement = element.querySelector('.date-range time');
        const locationElement = element.querySelector('.location');
        const descriptionElement = element.querySelector('.show-more-less-text__text--more');

        const experience: ParseHumanWorkExperience = {
          job_title: titleElement ? normalizeText(titleElement.textContent || '') : '',
          company_name: companyElement ? normalizeText(companyElement.textContent || '') : '',
          company_url: companyLinkElement ? (companyLinkElement as HTMLAnchorElement).href : '',
          start_date: '',
          end_date: '',
          location: locationElement ? normalizeText(locationElement.textContent || '') : '',
          description: descriptionElement ? normalizeText(descriptionElement.textContent || '') : ''
        };

        // Parse date range
        if (dateElement) {
          const dateText = normalizeText(dateElement.textContent || '');
          const dateMatch = dateText.match(/(\w+\s+\d{4})\s*[-–]\s*(\w+\s+\d{4}|Present)/);
          if (dateMatch) {
            experience.start_date = dateMatch[1];
            experience.end_date = dateMatch[2] === 'Present' ? '' : dateMatch[2];
          }
        }

        if (experience.job_title || experience.company_name) {
          experiences.push(experience);
        }
      });

      return experiences;
    } catch (error) {
      console.warn('Failed to parse work experience:', error);
      return [];
    }
  }, []);

  /**
   * Parse educational experience
   */
  const parseEducationalExperience = useCallback((parser: HTMLParser): ParseHumanEducationExperience[] => {
    const educations: ParseHumanEducationExperience[] = [];

    try {
      const educationElements = parser.getElements('ul.education__list li');

      educationElements.forEach(element => {
        const schoolElement = element.querySelector('h3');
        const schoolLinkElement = element.querySelector('h3 a');
        const degreeElement = element.querySelector('h4 span[aria-hidden="true"]');
        const fieldElement = element.querySelector('.field-of-study');
        const dateElement = element.querySelector('.date-range time');
        const descriptionElement = element.querySelector('.show-more-less-text__text--more');

        const education: ParseHumanEducationExperience = {
          school_name: schoolElement ? normalizeText(schoolElement.textContent || '') : '',
          school_url: schoolLinkElement ? (schoolLinkElement as HTMLAnchorElement).href : '',
          degree: degreeElement ? normalizeText(degreeElement.textContent || '') : '',
          field_of_study: fieldElement ? normalizeText(fieldElement.textContent || '') : '',
          start_date: '',
          end_date: '',
          description: descriptionElement ? normalizeText(descriptionElement.textContent || '') : ''
        };

        // Parse date range
        if (dateElement) {
          const dateText = normalizeText(dateElement.textContent || '');
          const dateMatch = dateText.match(/(\d{4})\s*[-–]\s*(\d{4})/);
          if (dateMatch) {
            education.start_date = dateMatch[1];
            education.end_date = dateMatch[2];
          }
        }

        if (education.school_name || education.degree) {
          educations.push(education);
        }
      });

      return educations;
    } catch (error) {
      console.warn('Failed to parse educational experience:', error);
      return [];
    }
  }, []);

  /**
   * Parse language abilities
   */
  const parseLanguageAbility = useCallback((parser: HTMLParser): ParseHumanLanguageAbility[] => {
    const languages: ParseHumanLanguageAbility[] = [];

    try {
      const languageElements = parser.getElements('section[data-section="languages"] ul li');

      languageElements.forEach(element => {
        const languageElement = element.querySelector('h3');
        const proficiencyElement = element.querySelector('h4');

        const language: ParseHumanLanguageAbility = {
          language: languageElement ? normalizeText(languageElement.textContent || '') : '',
          proficiency: proficiencyElement ? normalizeText(proficiencyElement.textContent || '') : ''
        };

        if (language.language) {
          languages.push(language);
        }
      });

      return languages;
    } catch (error) {
      console.warn('Failed to parse language abilities:', error);
      return [];
    }
  }, []);

  /**
   * Parse not logged in human page
   */
  const parseNotLoginHuman = useCallback((htmlContent: string): NotLoginHumanData => {
    const parser = new HTMLParser(htmlContent);

    // Initialize result with base info
    const baseInfo = parseHumanBaseInfo(parser);
    const result: NotLoginHumanData = {
      country_code: baseInfo.country_code || '',
      human_avatar: baseInfo.human_avatar || '',
      full_name: '',
      linkedin_username: '',
      job_title: '',
      location: '',
      about: '',
      work_experience: [],
      educational_experience: [],
      language_ability: []
    };

    try {
      // Extract basic profile info
      const nameElements = parser.xpath('h1.top-card-layout__title');
      result.full_name = nameElements.length > 0 ? normalizeText(nameElements[0]) : '';

      const titleElements = parser.xpath('h2.top-card-layout__headline');
      result.job_title = titleElements.length > 0 ? normalizeText(titleElements[0]) : '';

      const locationElements = parser.xpath('.top-card__subline-item');
      result.location = locationElements.length > 0 ? normalizeText(locationElements[0]) : '';

      // Extract LinkedIn username from canonical URL
      const canonicalElements = parser.xpath('link[rel="canonical"]/@href');
      if (canonicalElements.length > 0) {
        result.linkedin_username = LinkedInPatterns.extractLinkedInId(canonicalElements[0]);
      }

      // Extract about section
      const aboutElements = parser.xpath('.core-section-container__content .show-more-less-text__text--more');
      result.about = aboutElements.length > 0 ? normalizeText(aboutElements[0]) : '';

      // Parse experiences and education
      result.work_experience = parseWorkExperience(parser);
      result.educational_experience = parseEducationalExperience(parser);
      result.language_ability = parseLanguageAbility(parser);

      return result;
    } catch (error) {
      console.warn('Failed to parse not login human data:', error);
      return result;
    }
  }, [parseHumanBaseInfo, parseWorkExperience, parseEducationalExperience, parseLanguageAbility]);

  /**
   * Parse logged in human page
   */
  const parseLoginHuman = useCallback((htmlContent: string): LoginHumanData => {
    try {
      const cleanedHtml = customUnescape(htmlContent);

      // Extract human name from URL pattern
      const humanNameMatch = cleanedHtml.match(/\/in\/(.*?)\/overlay\//);
      const humanName = humanNameMatch ? humanNameMatch[1] : '';

      return {
        html_str: cleanedHtml,
        human_name: humanName
      };
    } catch (error) {
      console.warn('Failed to parse login human data:', error);
      return {
        html_str: htmlContent,
        human_name: ''
      };
    }
  }, []);

  /**
   * Main parsing function
   */
  const parseHumanInfo = useCallback(async (
    htmlContent: string,
    config: ParserConfig = { from_type: 'html', login_status: 'not_logged_in' }
  ): Promise<NotLoginHumanData | LoginHumanData> => {
    setIsLoading(true);
    setError(null);

    try {
      const cleanedHtml = customUnescape(htmlContent);

      if (config.login_status === 'logged_in') {
        return parseLoginHuman(cleanedHtml);
      } else {
        return parseNotLoginHuman(cleanedHtml);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown parsing error';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [parseNotLoginHuman, parseLoginHuman]);

  return {
    parseHumanInfo,
    parseNotLoginHuman,
    parseLoginHuman,
    parseWorkExperience,
    parseEducationalExperience,
    parseLanguageAbility,
    isLoading,
    error
  };
}

/**
 * Human Info Parser Component
 */
export const HumanInfoParser: React.FC<HumanInfoParserProps> = ({
  htmlContent,
  onParseComplete,
  onParseError,
  config,
  className
}) => {
  const { parseHumanInfo, isLoading, error } = useHumanInfoParser();

  const handleParse = useCallback(async () => {
    try {
      const result = await parseHumanInfo(htmlContent, config);
      onParseComplete?.(result);
    } catch (err) {
      const parseError = {
        code: 'HUMAN_PARSE_ERROR',
        message: err instanceof Error ? err.message : 'Unknown error',
        details: err
      };
      onParseError?.(parseError);
    }
  }, [htmlContent, config, parseHumanInfo, onParseComplete, onParseError]);

  React.useEffect(() => {
    if (htmlContent) {
      handleParse();
    }
  }, [htmlContent, handleParse]);

  if (isLoading) {
    return <div className={`${className} text-center`}>解析人物信息中...</div>;
  }

  if (error) {
    return <div className={`${className} text-red-500`}>解析错误: {error}</div>;
  }

  return null;
};
