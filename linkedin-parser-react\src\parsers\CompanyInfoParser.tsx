﻿/**
 * LinkedIn Company Info Parser Component
 * Converts parse_company_info_page.py functionality to React
 */

import React, { useState, useCallback } from 'react';
import { BaseParserProps, ParserConfig } from '@/types';
import {
  HTMLParser,
  LinkedInPatterns,
  customUnescape,
  safeString,
  normalizeText,
  safeJsonParse
} from '@/utils';

interface CompanyInfoParserProps extends BaseParserProps {
  config?: ParserConfig;
}

interface CompanyOverview {
  [key: string]: string;
}

interface CompanyEmployee {
  human_full_name: string;
  human_url: string;
  human_id: string;
  job_title: string;
}

interface NotLoginCompanyData {
  name?: string;
  url?: string;
  logo?: string;
  description?: string;
  website?: string;
  industry?: string;
  companySize?: string;
  headquarters?: string;
  founded?: string;
  specialties?: string;
  overview: CompanyOverview;
  jod_url: string;
  employees: CompanyEmployee[];
}

interface LoginCompanyData {
  html_str: string;
  company_name: string;
}

/**
 * Company Info Parser Hook
 */
export function useCompanyInfoParser() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Parse not logged in company page
   */
  const parseNotLoginCompany = useCallback((htmlContent: string): NotLoginCompanyData => {
    const parser = new HTMLParser(htmlContent);
    const result: NotLoginCompanyData = {
      overview: {},
      jod_url: '',
      employees: []
    };

    try {
      // Extract JSON-LD data
      const jsonLdData = parser.extractJsonLD();
      if (jsonLdData.length > 0) {
        const mainData = jsonLdData[0];
        const graphData = mainData['@graph'] || [];

        if (graphData.length > 0) {
          const companyData = graphData[graphData.length - 1];
          const addressData = graphData.length > 9 ? graphData[9].address || {} : {};

          Object.assign(result, companyData, addressData);
        }
      }

      // Extract overview information
      const overviewElements = parser.getElements('dl.mt-6 div');
      overviewElements.forEach((element, index) => {
        const keyElements = parser.getDocument().querySelectorAll(`dl.mt-6 div:nth-child(${index + 1}) dt`);
        const valueElements = parser.getDocument().querySelectorAll(`dl.mt-6 div:nth-child(${index + 1}) dd`);

        if (keyElements.length > 0 && valueElements.length > 0) {
          const key = normalizeText(keyElements[0].textContent || '');
          const value = normalizeText(valueElements[0].textContent || '');

          if (key && value) {
            result.overview[key] = value;
          }
        }
      });

      // Extract job URL
      const jobUrlElements = parser.xpath('a[data-tracking-control-name="nav_type_jobs"]/@href');
      result.jod_url = jobUrlElements.length > 0 ? jobUrlElements[0] : '';

      // Extract employees
      const employeeElements = parser.getElements('section[data-test-id="employees-at"] ul li');
      employeeElements.forEach(employeeElement => {
        const nameElements = employeeElement.querySelectorAll('a div h3');
        const urlElements = employeeElement.querySelectorAll('a');
        const titleElements = employeeElement.querySelectorAll('a div h4');

        if (nameElements.length > 0 && urlElements.length > 0) {
          const humanFullName = normalizeText(nameElements[0].textContent || '');
          const humanUrl = (urlElements[0] as HTMLAnchorElement).href || '';
          const jobTitle = titleElements.length > 0 ? normalizeText(titleElements[0].textContent || '') : '';

          if (humanFullName && humanUrl) {
            const humanId = LinkedInPatterns.extractLinkedInId(humanUrl);

            if (humanId) {
              result.employees.push({
                human_full_name: humanFullName,
                human_url: customUnescape(humanUrl),
                human_id: humanId,
                job_title: jobTitle
              });
            }
          }
        }
      });

      return result;
    } catch (error) {
      console.warn('Failed to parse not login company data:', error);
      return result;
    }
  }, []);

  /**
   * Parse logged in company page
   */
  const parseLoginCompany = useCallback((htmlContent: string): LoginCompanyData => {
    const parser = new HTMLParser(htmlContent);

    try {
      // Extract company name from URL
      const canonicalElements = parser.xpath('a[aria-current="page"]/@href');
      let companyName = '';

      if (canonicalElements.length > 0) {
        const companyNameMatch = canonicalElements[0].match(/\/company\/(.*?)\//);
        companyName = companyNameMatch ? companyNameMatch[1] : '';
      }

      return {
        html_str: htmlContent,
        company_name: companyName
      };
    } catch (error) {
      console.warn('Failed to parse login company data:', error);
      return {
        html_str: htmlContent,
        company_name: ''
      };
    }
  }, []);

  /**
   * Main parsing function
   */
  const parseCompanyInfo = useCallback(async (
    htmlContent: string,
    config: ParserConfig = { from_type: 'html', login_status: 'not_logged_in' }
  ): Promise<NotLoginCompanyData | LoginCompanyData> => {
    setIsLoading(true);
    setError(null);

    try {
      const cleanedHtml = customUnescape(htmlContent);

      if (config.login_status === 'logged_in') {
        return parseLoginCompany(cleanedHtml);
      } else {
        return parseNotLoginCompany(cleanedHtml);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown parsing error';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [parseNotLoginCompany, parseLoginCompany]);

  return {
    parseCompanyInfo,
    parseNotLoginCompany,
    parseLoginCompany,
    isLoading,
    error
  };
}

/**
 * Company Info Parser Component
 */
export const CompanyInfoParser: React.FC<CompanyInfoParserProps> = ({
  htmlContent,
  onParseComplete,
  onParseError,
  config,
  className
}) => {
  const { parseCompanyInfo, isLoading, error } = useCompanyInfoParser();

  const handleParse = useCallback(async () => {
    try {
      const result = await parseCompanyInfo(htmlContent, config);
      onParseComplete?.(result);
    } catch (err) {
      const parseError = {
        code: 'COMPANY_PARSE_ERROR',
        message: err instanceof Error ? err.message : 'Unknown error',
        details: err
      };
      onParseError?.(parseError);
    }
  }, [htmlContent, config, parseCompanyInfo, onParseComplete, onParseError]);

  React.useEffect(() => {
    if (htmlContent) {
      handleParse();
    }
  }, [htmlContent, handleParse]);

  if (isLoading) {
    return <div className={`${className} text-center`}>解析公司信息中...</div>;
  }

  if (error) {
    return <div className={`${className} text-red-500`}>解析错误: {error}</div>;
  }

  return null;
};
