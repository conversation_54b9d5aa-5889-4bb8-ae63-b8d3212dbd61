﻿/**
 * App Component Tests
 * Test suite for the main application component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import App from '../App';

// Mock file for testing
const mockFile = new File(['<html><body>Test HTML</body></html>'], 'test.html', {
  type: 'text/html'
});

describe('App Component', () => {
  test('should render main navigation tabs', () => {
    render(<App />);

    expect(screen.getByText('输入')).toBeInTheDocument();
    expect(screen.getByText('结果')).toBeInTheDocument();
    expect(screen.getByText('设置')).toBeInTheDocument();
    expect(screen.getByText('关于')).toBeInTheDocument();
  });

  test('should render header with title', () => {
    render(<App />);

    expect(screen.getByText('LinkedIn Parser')).toBeInTheDocument();
    expect(screen.getByText('React 18.2.18 实现')).toBeInTheDocument();
  });

  test('should start with input tab active', () => {
    render(<App />);

    expect(screen.getByText('HTML 内容输入')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('粘贴 LinkedIn 页面的 HTML 内容...')).toBeInTheDocument();
  });

  test('should switch between tabs', async () => {
    const user = userEvent.setup();
    render(<App />);

    // Click on settings tab
    await user.click(screen.getByText('设置'));
    expect(screen.getByText('解析设置')).toBeInTheDocument();

    // Click on about tab
    await user.click(screen.getByText('关于'));
    expect(screen.getByText('关于 LinkedIn Parser')).toBeInTheDocument();

    // Click back to input tab
    await user.click(screen.getByText('输入'));
    expect(screen.getByText('HTML 内容输入')).toBeInTheDocument();
  });

  test('should handle HTML content input', async () => {
    const user = userEvent.setup();
    render(<App />);

    const textarea = screen.getByPlaceholderText('粘贴 LinkedIn 页面的 HTML 内容...');
    await user.type(textarea, '<html><body>Test content</body></html>');

    expect(textarea).toHaveValue('<html><body>Test content</body></html>');
  });

  test('should handle file upload', async () => {
    const user = userEvent.setup();
    render(<App />);

    const fileInput = screen.getByLabelText('上传 HTML 文件');
    await user.upload(fileInput, mockFile);

    await waitFor(() => {
      const textarea = screen.getByPlaceholderText('粘贴 LinkedIn 页面的 HTML 内容...');
      expect(textarea).toHaveValue('<html><body>Test HTML</body></html>');
    });
  });

  test('should handle page type selection', async () => {
    const user = userEvent.setup();
    render(<App />);

    const pageTypeSelect = screen.getByDisplayValue('自动检测');
    await user.selectOptions(pageTypeSelect, 'search');

    expect(screen.getByDisplayValue('搜索页面')).toBeInTheDocument();
  });

  test('should handle login status selection', async () => {
    const user = userEvent.setup();
    render(<App />);

    const loginStatusSelect = screen.getByDisplayValue('未登录');
    await user.selectOptions(loginStatusSelect, 'logged_in');

    expect(screen.getByDisplayValue('已登录')).toBeInTheDocument();
  });

  test('should show error when parsing without content', async () => {
    const user = userEvent.setup();
    render(<App />);

    const parseButton = screen.getByText('开始解析');
    await user.click(parseButton);

    expect(screen.getByText('请输入 HTML 内容')).toBeInTheDocument();
  });

  test('should disable parse button when loading', async () => {
    const user = userEvent.setup();
    render(<App />);

    const textarea = screen.getByPlaceholderText('粘贴 LinkedIn 页面的 HTML 内容...');
    await user.type(textarea, '<html><body>Test</body></html>');

    const parseButton = screen.getByText('开始解析');
    expect(parseButton).not.toBeDisabled();

    // The button should be enabled when there's content
    expect(parseButton).toBeEnabled();
  });

  test('should show results tab when no results', async () => {
    const user = userEvent.setup();
    render(<App />);

    await user.click(screen.getByText('结果'));

    expect(screen.getByText('暂无解析结果')).toBeInTheDocument();
    expect(screen.getByText('请先在输入页面解析 HTML 内容')).toBeInTheDocument();
  });

  test('should render settings tab correctly', async () => {
    const user = userEvent.setup();
    render(<App />);

    await user.click(screen.getByText('设置'));

    expect(screen.getByText('解析模式')).toBeInTheDocument();
    expect(screen.getByText('默认登录状态')).toBeInTheDocument();
  });

  test('should render about tab correctly', async () => {
    const user = userEvent.setup();
    render(<App />);

    await user.click(screen.getByText('关于'));

    expect(screen.getByText('项目信息')).toBeInTheDocument();
    expect(screen.getByText('支持的页面类型')).toBeInTheDocument();
    expect(screen.getByText('功能特性')).toBeInTheDocument();
  });
});

describe('App Integration', () => {
  test('should handle complete parsing workflow', async () => {
    const user = userEvent.setup();
    render(<App />);

    // Input HTML content
    const textarea = screen.getByPlaceholderText('粘贴 LinkedIn 页面的 HTML 内容...');
    await user.type(textarea, '<html><body class="search-results">Test</body></html>');

    // Select page type
    const pageTypeSelect = screen.getByDisplayValue('自动检测');
    await user.selectOptions(pageTypeSelect, 'search');

    // Start parsing
    const parseButton = screen.getByText('开始解析');
    await user.click(parseButton);

    // Should switch to results tab automatically
    await waitFor(() => {
      expect(screen.getByText('解析结果')).toBeInTheDocument();
    });
  });
});
