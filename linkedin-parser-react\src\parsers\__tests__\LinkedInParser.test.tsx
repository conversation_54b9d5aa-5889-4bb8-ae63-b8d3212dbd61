﻿/**
 * LinkedIn Parser Tests
 * Test suite for LinkedIn parsing components
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { LinkedInParser, useLinkedInParser } from '../LinkedInParser';

// Mock HTML samples
const mockSearchHTML = `
  <html>
    <body>
      <div class="search-results">
        <code>{"included": [{"entityUrn": "urn:li:fsd_profile", "backendUrn": "urn:li:person:123", "title": {"text": "<PERSON>"}}]}</code>
      </div>
    </body>
  </html>
`;

const mockCompanyHTML = `
  <html>
    <body>
      <div class="company-overview">
        <script type="application/ld+json">
          {"@type": "Organization", "name": "Microsoft"}
        </script>
      </div>
    </body>
  </html>
`;

const mockHumanHTML = `
  <html>
    <body>
      <div class="profile-top-card">
        <h1><PERSON></h1>
        <h2>Software Engineer</h2>
      </div>
    </body>
  </html>
`;

describe('useLinkedInParser', () => {
  test('should detect search page type', () => {
    const TestComponent = () => {
      const { detectPageType } = useLinkedInParser();
      const pageType = detectPageType(mockSearchHTML);
      return <div data-testid="page-type">{pageType}</div>;
    };

    render(<TestComponent />);
    expect(screen.getByTestId('page-type')).toHaveTextContent('search');
  });

  test('should detect company page type', () => {
    const TestComponent = () => {
      const { detectPageType } = useLinkedInParser();
      const pageType = detectPageType(mockCompanyHTML);
      return <div data-testid="page-type">{pageType}</div>;
    };

    render(<TestComponent />);
    expect(screen.getByTestId('page-type')).toHaveTextContent('company_info');
  });

  test('should detect human page type', () => {
    const TestComponent = () => {
      const { detectPageType } = useLinkedInParser();
      const pageType = detectPageType(mockHumanHTML);
      return <div data-testid="page-type">{pageType}</div>;
    };

    render(<TestComponent />);
    expect(screen.getByTestId('page-type')).toHaveTextContent('human_info');
  });
});

describe('LinkedInParser Component', () => {
  test('should render loading state', () => {
    const mockOnParseComplete = jest.fn();

    render(
      <LinkedInParser
        htmlContent={mockSearchHTML}
        onParseComplete={mockOnParseComplete}
      />
    );

    expect(screen.getByText('解析 LinkedIn 页面中...')).toBeInTheDocument();
  });

  test('should call onParseComplete when parsing succeeds', async () => {
    const mockOnParseComplete = jest.fn();

    render(
      <LinkedInParser
        htmlContent={mockSearchHTML}
        onParseComplete={mockOnParseComplete}
        pageType="search"
      />
    );

    await waitFor(() => {
      expect(mockOnParseComplete).toHaveBeenCalled();
    });
  });

  test('should call onParseError when parsing fails', async () => {
    const mockOnParseError = jest.fn();
    const invalidHTML = '<invalid>';

    render(
      <LinkedInParser
        htmlContent={invalidHTML}
        onParseError={mockOnParseError}
      />
    );

    await waitFor(() => {
      expect(mockOnParseError).toHaveBeenCalled();
    });
  });

  test('should display success state with stats', async () => {
    const mockOnParseComplete = jest.fn();

    render(
      <LinkedInParser
        htmlContent={mockSearchHTML}
        onParseComplete={mockOnParseComplete}
        pageType="search"
      />
    );

    await waitFor(() => {
      expect(screen.getByText('解析完成')).toBeInTheDocument();
    });
  });
});

describe('Parser Integration', () => {
  test('should parse search page and return results', async () => {
    const TestComponent = () => {
      const { parseContent, results, isLoading } = useLinkedInParser();

      React.useEffect(() => {
        parseContent(mockSearchHTML, 'search');
      }, [parseContent]);

      if (isLoading) return <div>Loading...</div>;
      if (results) return <div data-testid="results">Success</div>;
      return <div>No results</div>;
    };

    render(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('results')).toBeInTheDocument();
    });
  });

  test('should handle auto-detection correctly', async () => {
    const TestComponent = () => {
      const { parseContent, results } = useLinkedInParser();

      React.useEffect(() => {
        parseContent(mockSearchHTML, 'auto_detect');
      }, [parseContent]);

      return (
        <div data-testid="page-type">
          {results?.pageType || 'none'}
        </div>
      );
    };

    render(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('page-type')).toHaveTextContent('search');
    });
  });
});
