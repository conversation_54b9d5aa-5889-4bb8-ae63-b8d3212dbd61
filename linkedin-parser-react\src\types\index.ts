/**
 * Main types export file
 */

export * from './parseModels';

/**
 * Common utility types
 */
export type Optional<T> = T | undefined;
export type Nullable<T> = T | null;

/**
 * HTML parsing related types
 */
export interface HTMLParseOptions {
  preserveWhitespace?: boolean;
  caseSensitive?: boolean;
  validateStructure?: boolean;
}

/**
 * XPath query result type
 */
export type XPathResult = string | string[] | Element | Element[] | null;

/**
 * Parser status types
 */
export type ParserStatus = 'idle' | 'parsing' | 'success' | 'error';

/**
 * Component props for parser components
 */
export interface BaseParserProps {
  htmlContent: string;
  onParseComplete?: (result: any) => void;
  onParseError?: (error: ParseError) => void;
  className?: string;
}

/**
 * UI State types
 */
export interface UIState {
  isLoading: boolean;
  error: string | null;
  activeTab: string;
  showResults: boolean;
}

/**
 * File upload types
 */
export interface FileUploadState {
  file: File | null;
  content: string;
  isUploading: boolean;
  error: string | null;
}
