﻿/**
 * LinkedIn Parsers Export
 * Main entry point for all LinkedIn parsing functionality
 */

// Main parser (entry point)
export {
  LinkedInParser,
  useLinkedInParser,
  type PageType
} from './LinkedInParser';

// Individual parsers
export {
  SearchPageParser,
  useSearchPageParser
} from './SearchPageParser';

export {
  CompanyInfoParser,
  useCompanyInfoParser
} from './CompanyInfoParser';

export {
  HumanInfoParser,
  useHumanInfoParser
} from './HumanInfoParser';

// Re-export types
export type {
  ParseResult,
  ParseSearchAllPeople,
  ParseSearchAllCompany,
  ParseSearchAllPost,
  ParseSearchAllPostCount,
  ParseHumanWorkExperience,
  ParseHumanEducationExperience,
  ParseHumanLanguageAbility,
  ParserConfig,
  BaseParserProps
} from '@/types';
