/**
 * HTML Parser Utilities
 * Replaces Python lxml functionality with browser-native DOMParser
 */

import { HTMLParseOptions, XPathResult } from '@/types';

/**
 * Custom HTML entity decoder (replaces Python's custom_unescape)
 */
export function customUnescape(text: string, entities: Record<string, string> = {}): string {
  const defaultEntities = {
    '\n': '',
    '\r': '',
    '\t': '',
    '\u3000': '',
    '\xa0': '',
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#39;': "'",
    ...entities
  };

  let result = text;
  for (const [entity, replacement] of Object.entries(defaultEntities)) {
    result = result.replace(new RegExp(entity, 'g'), replacement);
  }
  
  return result.trim();
}

/**
 * HTML Parser class - replaces Python's etree.HTML functionality
 */
export class HTMLParser {
  private document: Document;
  private parser: DOMParser;

  constructor(htmlString: string, options: HTMLParseOptions = {}) {
    this.parser = new DOMParser();
    
    // Clean and prepare HTML string
    const cleanedHtml = this.preprocessHTML(htmlString);
    
    // Parse HTML
    this.document = this.parser.parseFromString(cleanedHtml, 'text/html');
    
    // Check for parsing errors
    const parserError = this.document.querySelector('parsererror');
    if (parserError) {
      console.warn('HTML parsing warning:', parserError.textContent);
    }
  }

  /**
   * Preprocess HTML string (similar to custom_unescape in Python)
   */
  private preprocessHTML(html: string): string {
    return customUnescape(html, {
      '\n': '',
      '\t': '',
    });
  }

  /**
   * XPath query implementation using CSS selectors and DOM traversal
   * Converts common XPath patterns to CSS selectors or DOM methods
   */
  public xpath(xpathExpression: string): string[] {
    try {
      // Handle common XPath patterns
      if (xpathExpression.includes('//text()')) {
        return this.getTextContent(xpathExpression);
      }
      
      if (xpathExpression.includes('@')) {
        return this.getAttributeValues(xpathExpression);
      }
      
      // Convert XPath to CSS selector for element selection
      const cssSelector = this.convertXPathToCSS(xpathExpression);
      const elements = this.document.querySelectorAll(cssSelector);
      
      return Array.from(elements).map(el => el.textContent?.trim() || '').filter(Boolean);
    } catch (error) {
      console.warn('XPath query failed:', xpathExpression, error);
      return [];
    }
  }

  /**
   * Get text content from XPath expression
   */
  private getTextContent(xpathExpression: string): string[] {
    const selectorPart = xpathExpression.replace('//text()', '').replace('/text()', '');
    const cssSelector = this.convertXPathToCSS(selectorPart);
    
    if (!cssSelector) {
      // Handle cases like './/script[@type="application/ld+json"]//text()'
      const elements = this.document.querySelectorAll('script[type="application/ld+json"]');
      return Array.from(elements).map(el => el.textContent || '').filter(Boolean);
    }
    
    const elements = this.document.querySelectorAll(cssSelector);
    return Array.from(elements).map(el => el.textContent?.trim() || '').filter(Boolean);
  }

  /**
   * Get attribute values from XPath expression
   */
  private getAttributeValues(xpathExpression: string): string[] {
    const attrMatch = xpathExpression.match(/@([^\/\]]+)/);
    if (!attrMatch) return [];
    
    const attributeName = attrMatch[1];
    const selectorPart = xpathExpression.split('/@')[0];
    const cssSelector = this.convertXPathToCSS(selectorPart);
    
    const elements = this.document.querySelectorAll(cssSelector);
    return Array.from(elements)
      .map(el => el.getAttribute(attributeName) || '')
      .filter(Boolean);
  }

  /**
   * Convert common XPath patterns to CSS selectors
   */
  private convertXPathToCSS(xpath: string): string {
    if (!xpath || xpath === '.') return '';
    
    let css = xpath
      // Remove leading dots and slashes
      .replace(/^\.?\/+/, '')
      // Convert descendant selectors
      .replace(/\/\//g, ' ')
      .replace(/\//g, ' > ')
      // Convert attribute selectors
      .replace(/\[@([^=]+)="([^"]+)"\]/g, '[$1="$2"]')
      .replace(/\[@([^=]+)\]/g, '[$1]')
      // Convert class selectors
      .replace(/\[@class="([^"]+)"\]/g, '.$1')
      // Handle contains() function for class
      .replace(/\[contains\(concat\(" ", normalize-space\(@class\), " "\), " ([^"]+) "\)\]/g, '.$1')
      // Convert position selectors
      .replace(/\[(\d+)\]/g, ':nth-child($1)')
      // Clean up extra spaces
      .replace(/\s+/g, ' ')
      .trim();
    
    return css;
  }

  /**
   * Get elements by XPath-like selector
   */
  public getElements(selector: string): Element[] {
    const cssSelector = this.convertXPathToCSS(selector);
    return Array.from(this.document.querySelectorAll(cssSelector));
  }

  /**
   * Get single element by XPath-like selector
   */
  public getElement(selector: string): Element | null {
    const cssSelector = this.convertXPathToCSS(selector);
    return this.document.querySelector(cssSelector);
  }

  /**
   * Get the parsed document
   */
  public getDocument(): Document {
    return this.document;
  }

  /**
   * Extract JSON-LD data (common in LinkedIn pages)
   */
  public extractJsonLD(): any[] {
    const scripts = this.document.querySelectorAll('script[type="application/ld+json"]');
    const jsonData: any[] = [];
    
    scripts.forEach(script => {
      try {
        const data = JSON.parse(script.textContent || '');
        jsonData.push(data);
      } catch (error) {
        console.warn('Failed to parse JSON-LD:', error);
      }
    });
    
    return jsonData;
  }

  /**
   * Extract code blocks (for LinkedIn's embedded JSON data)
   */
  public extractCodeBlocks(): string[] {
    const codeElements = this.document.querySelectorAll('code');
    return Array.from(codeElements)
      .map(code => code.textContent || '')
      .filter(content => content.trim().length > 0);
  }
}

/**
 * Utility function to create HTMLParser instance
 */
export function parseHTML(htmlString: string, options?: HTMLParseOptions): HTMLParser {
  return new HTMLParser(htmlString, options);
}

/**
 * Extract specific data patterns commonly found in LinkedIn pages
 */
export const LinkedInPatterns = {
  /**
   * Extract LinkedIn ID from URL
   */
  extractLinkedInId: (url: string): string => {
    const match = url.match(/linkedin\.com\/(?:in|company)\/([^\/?]+)/);
    return match ? decodeURIComponent(match[1]) : '';
  },

  /**
   * Extract company name from URL
   */
  extractCompanyName: (url: string): string => {
    const match = url.match(/\/company\/(.*?)\//);
    return match ? match[1] : '';
  },

  /**
   * Extract person username from URL
   */
  extractPersonUsername: (url: string): string => {
    const match = url.match(/\/in\/(.*?)\/overlay\//);
    return match ? match[1] : '';
  },

  /**
   * Clean LinkedIn URL (remove query parameters)
   */
  cleanLinkedInUrl: (url: string): string => {
    return url.split('?')[0].trim().replace(/\/$/, '').replace('https://www.', '');
  }
};
