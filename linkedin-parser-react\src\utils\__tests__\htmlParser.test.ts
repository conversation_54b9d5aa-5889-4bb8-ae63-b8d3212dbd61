﻿/**
 * HTML Parser Tests
 * Test suite for HTML parsing utilities
 */

import { HTMLParser, customUnescape, LinkedInPatterns } from '../htmlParser';

describe('customUnescape', () => {
  test('should remove default entities', () => {
    const input = 'Hello\nWorld\t&amp;&lt;&gt;';
    const result = customUnescape(input);
    expect(result).toBe('HelloWorld&<>');
  });

  test('should apply custom entities', () => {
    const input = 'Hello\nWorld';
    const entities = { '\n': ' ' };
    const result = customUnescape(input, entities);
    expect(result).toBe('Hello World');
  });

  test('should trim whitespace', () => {
    const input = '  Hello World  ';
    const result = customUnescape(input);
    expect(result).toBe('Hello World');
  });
});

describe('LinkedInPatterns', () => {
  test('should extract LinkedIn ID from URL', () => {
    const url = 'https://www.linkedin.com/in/john-doe-123/';
    const result = LinkedInPatterns.extractLinkedInId(url);
    expect(result).toBe('john-doe-123');
  });

  test('should extract company name from URL', () => {
    const url = 'https://www.linkedin.com/company/microsoft/';
    const result = LinkedInPatterns.extractCompanyName(url);
    expect(result).toBe('microsoft');
  });

  test('should extract person username from overlay URL', () => {
    const url = 'https://www.linkedin.com/in/john-doe/overlay/';
    const result = LinkedInPatterns.extractPersonUsername(url);
    expect(result).toBe('john-doe');
  });

  test('should clean LinkedIn URL', () => {
    const url = 'https://www.linkedin.com/in/john-doe/?param=value';
    const result = LinkedInPatterns.cleanLinkedInUrl(url);
    expect(result).toBe('linkedin.com/in/john-doe');
  });
});

describe('HTMLParser', () => {
  const sampleHTML = `
    <html>
      <head>
        <script type="application/ld+json">
          {"@type": "Person", "name": "John Doe"}
        </script>
      </head>
      <body>
        <div class="profile">
          <h1>John Doe</h1>
          <p class="title">Software Engineer</p>
          <a href="https://linkedin.com/company/microsoft" data-test="company-link">Microsoft</a>
        </div>
        <code>{"data": "test"}</code>
      </body>
    </html>
  `;

  let parser: HTMLParser;

  beforeEach(() => {
    parser = new HTMLParser(sampleHTML);
  });

  test('should parse HTML correctly', () => {
    expect(parser.getDocument()).toBeDefined();
    expect(parser.getDocument().querySelector('h1')?.textContent).toBe('John Doe');
  });

  test('should extract JSON-LD data', () => {
    const jsonData = parser.extractJsonLD();
    expect(jsonData).toHaveLength(1);
    expect(jsonData[0]).toEqual({ "@type": "Person", "name": "John Doe" });
  });

  test('should extract code blocks', () => {
    const codeBlocks = parser.extractCodeBlocks();
    expect(codeBlocks).toHaveLength(1);
    expect(codeBlocks[0]).toBe('{"data": "test"}');
  });

  test('should perform xpath queries', () => {
    const titles = parser.xpath('h1');
    expect(titles).toContain('John Doe');
  });

  test('should get elements by selector', () => {
    const elements = parser.getElements('h1');
    expect(elements).toHaveLength(1);
    expect(elements[0].textContent).toBe('John Doe');
  });

  test('should get single element', () => {
    const element = parser.getElement('p.title');
    expect(element?.textContent).toBe('Software Engineer');
  });
});

describe('HTMLParser XPath conversion', () => {
  test('should handle attribute selectors', () => {
    const html = '<div><a href="test" data-test="value">Link</a></div>';
    const parser = new HTMLParser(html);

    const links = parser.xpath('a[@href]');
    expect(links).toContain('Link');
  });

  test('should handle class selectors', () => {
    const html = '<div><p class="highlight">Text</p></div>';
    const parser = new HTMLParser(html);

    const texts = parser.xpath('p[@class="highlight"]');
    expect(texts).toContain('Text');
  });

  test('should handle descendant selectors', () => {
    const html = '<div><span><em>Nested</em></span></div>';
    const parser = new HTMLParser(html);

    const nested = parser.xpath('div//em');
    expect(nested).toContain('Nested');
  });
});
