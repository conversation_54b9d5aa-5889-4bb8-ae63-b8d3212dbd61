import { type EditableInputOrTextarea } from '../../utils';
export { getInputRange } from './getInputRange';
export { modifySelection } from './modifySelection';
export { moveSelection } from './moveSelection';
export { setSelectionPerMouseDown } from './setSelectionPerMouse';
export { modifySelectionPerMouseMove } from './modifySelectionPerMouse';
export { isAllSelected, selectAll } from './selectAll';
export { setSelectionRange } from './setSelectionRange';
export { setSelection } from './setSelection';
export { updateSelectionOnFocus } from './updateSelectionOnFocus';
export type SelectionRange = {
    node: EditableInputOrTextarea;
    start: number;
    end: number;
};
