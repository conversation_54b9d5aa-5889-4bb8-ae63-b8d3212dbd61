﻿/**
 * LinkedIn Search Page Parser Component
 * Converts parse_search_page.py functionality to React
 */

import React, { useState, useCallback } from 'react';
import {
  ParseSearchAllPeople,
  ParseSearchAllCompany,
  ParseSearchAllPost,
  ParseSearchAllPostCount,
  ParseResult,
  ParserConfig,
  BaseParserProps
} from '@/types';
import {
  HTMLParser,
  LinkedInPatterns,
  customUnescape,
  safeString,
  safeNumber,
  validateLinkedInId,
  validateLinkedInUrl,
  normalizeText
} from '@/utils';

interface SearchPageParserProps extends BaseParserProps {
  config?: ParserConfig;
}

/**
 * Search Page Parser Hook
 */
export function useSearchPageParser() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Parse search people from JSON data
   */
  const parseSearchPeople = useCallback((data: any): ParseSearchAllPeople | null => {
    try {
      const trackingUrn = safeString(data.trackingUrn);
      const linkedinId = trackingUrn.split(':').pop() || '';

      if (linkedinId === 'headless') {
        return null;
      }

      const navigationUrl = safeString(data.navigationUrl);
      const linkedinUrl = LinkedInPatterns.cleanLinkedInUrl(navigationUrl);
      const linkedinUsername = LinkedInPatterns.extractLinkedInId(linkedinUrl);
      console.log("linkedinUsername", linkedinUsername)

      const titleData = data.title || {};
      const fullName = safeString(titleData.text);

      const primarySubtitle = data.primarySubtitle || {};
      const personProfiles = safeString(primarySubtitle.text);

      const secondarySubtitle = data.secondarySubtitle || {};
      const region = safeString(secondarySubtitle.text);

      const image = data.image || {};
      const attributes = image.attributes || [];
      const personLogos = attributes.length > 0 ? JSON.stringify(attributes) : '';

      return {
        linkedin_id: validateLinkedInId(linkedinId),
        linkedin_username: decodeURIComponent(linkedinUsername),
        linkedin_url: validateLinkedInUrl(linkedinUrl),
        full_name: normalizeText(fullName),
        person_logos: personLogos,
        person_profiles: normalizeText(personProfiles),
        region: normalizeText(region),
        lid: linkedinId
      };
    } catch (error) {
      console.warn('Failed to parse search people data:', error);
      return null;
    }
  }, []);

  /**
   * Parse search company from JSON data
   */
  const parseSearchCompany = useCallback((data: any): ParseSearchAllCompany | null => {
    try {
      const trackingUrn = safeString(data.trackingUrn);
      const companyId = trackingUrn.split(':').pop() || '';

      const navigationUrl = safeString(data.navigationUrl);
      const linkedinUrl = LinkedInPatterns.cleanLinkedInUrl(navigationUrl);
      const companyLinkedinId = LinkedInPatterns.extractLinkedInId(linkedinUrl);

      const titleData = data.title || {};
      const companyName = safeString(titleData.text);

      const primarySubtitle = data.primarySubtitle || {};
      const companyProfiles = safeString(primarySubtitle.text);

      const image = data.image || {};
      const attributes = image.attributes || [];
      const companyLogos = attributes.length > 0 ? JSON.stringify(attributes) : '';

      const secondarySubtitle = data.secondarySubtitle || {};
      const jobInfo = safeString(secondarySubtitle.text);
      const jobNum = jobInfo.match(/(\d+)/)?.[1] || '';

      return {
        job_company_id: companyLinkedinId,
        job_company_linkedin_id: validateLinkedInId(companyId),
        job_company_linkedin_url: validateLinkedInUrl(linkedinUrl),
        job_company_name: normalizeText(companyName),
        company_logos: companyLogos,
        company_profiles: normalizeText(companyProfiles),
        job_search_url: '',
        job_num: jobNum,
        followers: ''
      };
    } catch (error) {
      console.warn('Failed to parse search company data:', error);
      return null;
    }
  }, []);

  /**
   * Parse search posts from JSON data
   */
  const parseSearchPosts = useCallback((data: any): ParseSearchAllPost | null => {
    try {
      const urn = safeString(data.urn);
      const postId = urn.split(':').pop() || '';

      const updateMetadata = data.updateMetadata || {};
      const urnId = safeString(updateMetadata.urn);
      const postUrl = `https://www.linkedin.com/feed/update/${urnId}`;

      const commentary = data.commentary || {};
      const text = commentary.text || {};
      const postContent = safeString(text.text);

      const createdAt = safeNumber(data.createdAt);
      const postTime = createdAt ? new Date(createdAt).toISOString() : '';

      return {
        post_id: postId,
        post_url: postUrl,
        post_content: normalizeText(postContent),
        post_time: postTime,
        people_model: undefined,
        company_model: undefined
      };
    } catch (error) {
      console.warn('Failed to parse search posts data:', error);
      return null;
    }
  }, []);

  /**
   * Parse post count data
   */
  const parseSearchPostCount = useCallback((data: any): ParseSearchAllPostCount | null => {
    try {
      const urn = safeString(data.urn);
      const postId = urn.split(':').pop() || '';

      return {
        post_id: postId,
        like_num: safeNumber(data.numLikes),
        comments_num: safeNumber(data.numComments),
        shares_num: safeNumber(data.numShares)
      };
    } catch (error) {
      console.warn('Failed to parse post count data:', error);
      return null;
    }
  }, []);

  /**
   * Parse JSON data from search page
   */
  const parseSearchPageJson = useCallback((jsonStr: string): [ParseResult, boolean] => {
    const result: ParseResult = {
      company_models: {},
      people_models: {},
      post_models: {},
      company_followee_models: {},
      people_followee_models: {},
      post_count_models: {}
    };

    try {
      const data = JSON.parse(jsonStr);
      const included = data.included || [];

      for (const item of included) {
        const entityUrn = safeString(item.entityUrn);
        const backendUrn = safeString(item.backendUrn);

        if (entityUrn.includes('urn:li:fsd_profile') && backendUrn.includes('urn:li:person')) {
          const parseData = parseSearchPeople(item);
          if (parseData && parseData.linkedin_id) {
            result.people_models[parseData.linkedin_id] = parseData;
          }
        } else if (entityUrn.includes('urn:li:fsd_company') && backendUrn.includes('urn:li:company')) {
          const parseData = parseSearchCompany(item);
          if (parseData && parseData.job_company_id) {
            result.company_models[parseData.job_company_id] = parseData;
          }
        } else if (entityUrn.includes('urn:li:fsd_update') && backendUrn.includes('urn:li:activity')) {
          const parseData = parseSearchPosts(item);
          if (parseData && parseData.post_id) {
            result.post_models[parseData.post_id] = parseData;
          }
        } else if (entityUrn.includes('urn:li:fsd_socialActivityCounts')) {
          const parseData = parseSearchPostCount(item);
          if (parseData && parseData.post_id) {
            result.post_count_models[parseData.post_id] = parseData;
          }
        }
      }

      return [result, true];
    } catch (error) {
      console.error('Failed to parse search page JSON:', error);
      return [result, false];
    }
  }, [parseSearchPeople, parseSearchCompany, parseSearchPosts, parseSearchPostCount]);

  /**
   * Main parsing function
   */
  const parseSearchPage = useCallback(async (
    htmlContent: string,
    config: ParserConfig = { from_type: 'html', login_status: 'not_logged_in' }
  ): Promise<ParseResult> => {
    setIsLoading(true);
    setError(null);

    try {
      const cleanedHtml = customUnescape(htmlContent);
      const parser = new HTMLParser(cleanedHtml);

      if (config.from_type === 'html') {
        const codeBlocks = parser.extractCodeBlocks();

        for (const codeContent of codeBlocks) {
          const [parseData, success] = parseSearchPageJson(codeContent);
          if (success && Object.keys(parseData.people_models).length > 0) {
            return parseData;
          }
        }
      }

      return {
        company_models: {},
        people_models: {},
        post_models: {},
        company_followee_models: {},
        people_followee_models: {},
        post_count_models: {}
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown parsing error';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [parseSearchPageJson]);

  return {
    parseSearchPage,
    parseSearchPeople,
    parseSearchCompany,
    parseSearchPosts,
    parseSearchPostCount,
    isLoading,
    error
  };
}

/**
 * Search Page Parser Component
 */
export const SearchPageParser: React.FC<SearchPageParserProps> = ({
  htmlContent,
  onParseComplete,
  onParseError,
  config,
  className
}) => {
  const { parseSearchPage, isLoading, error } = useSearchPageParser();

  const handleParse = useCallback(async () => {
    try {
      const result = await parseSearchPage(htmlContent, config);
      onParseComplete?.(result);
    } catch (err) {
      const parseError = {
        code: 'SEARCH_PARSE_ERROR',
        message: err instanceof Error ? err.message : 'Unknown error',
        details: err
      };
      onParseError?.(parseError);
    }
  }, [htmlContent, config, parseSearchPage, onParseComplete, onParseError]);

  React.useEffect(() => {
    if (htmlContent) {
      handleParse();
    }
  }, [htmlContent, handleParse]);

  if (isLoading) {
    return <div className={`${className} text-center`}>解析搜索页面中...</div>;
  }

  if (error) {
    return <div className={`${className} text-red-500`}>解析错误: {error}</div>;
  }

  return null;
};
