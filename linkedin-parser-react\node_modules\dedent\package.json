{"name": "dedent", "version": "1.6.0", "description": "A string tag that strips indentation from multi-line strings. ⬅️", "keywords": ["dedent", "tag", "multi-line string", "es6"], "homepage": "https://github.com/dmnd/dedent", "bugs": {"url": "https://github.com/dmnd/dedent/issues"}, "repository": {"type": "git", "url": "https://github.com/dmnd/dedent"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://desmondbrand.com"}, "type": "commonjs", "exports": {".": {"types": {"import": "./dist/dedent.d.mts", "require": "./dist/dedent.d.ts"}, "import": "./dist/dedent.mjs", "require": "./dist/dedent.js"}}, "main": "./dist/dedent.js", "module": "./dist/dedent.mjs", "types": "./dist/dedent.d.mts", "files": ["dist/", "macro.js", "package.json", "LICENSE.md", "README.md"], "scripts": {"build": "pnpm build:legacy && pnpm build:modern && pnpm build:types", "build:legacy": "BABEL_ENV=legacy babel src/dedent.ts --out-file dist/dedent.js", "build:modern": "BABEL_ENV=modern babel src/dedent.ts --out-file dist/dedent.mjs", "build:types": "tsup src/dedent.ts --dts-only", "format": "prettier \"**/*\" --ignore-unknown", "lint": "eslint . .*js --max-warnings 0 --report-unused-disable-directives", "lint:knip": "knip", "lint:md": "markdownlint \"**/*.md\" \".github/**/*.md\"", "lint:package-json": "npmPkgJsonLint .", "lint:packages": "pnpm dedupe --check", "lint:spelling": "cspell \"**\" \".github/**/*\"", "prepare": "husky install", "should-semantic-release": "should-semantic-release --verbose", "test": "jest", "tsc": "tsc"}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "devDependencies": {"@babel/cli": "^7.21.5", "@babel/preset-env": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@release-it/conventional-changelog": "^8.0.1", "@types/babel-plugin-macros": "^3.1.0", "@types/eslint": "^8.44.7", "@types/jest": "^29.5.3", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-tester": "^11.0.4", "console-fail-test": "^0.2.3", "cspell": "^8.0.0", "eslint": "^8.53.0", "eslint-plugin-deprecation": "^2.0.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-jsdoc": "^46.9.0", "eslint-plugin-jsonc": "^2.10.0", "eslint-plugin-markdown": "^3.0.1", "eslint-plugin-n": "^16.3.1", "eslint-plugin-no-only-tests": "^3.1.0", "eslint-plugin-perfectionist": "^2.3.0", "eslint-plugin-regexp": "^2.1.1", "eslint-plugin-yml": "^1.10.0", "husky": "^8.0.3", "jest": "^29.7.0", "jsonc-eslint-parser": "^2.4.0", "knip": "^2.41.0", "lint-staged": "^15.1.0", "markdownlint": "^0.31.1", "markdownlint-cli": "^0.37.0", "npm-package-json-lint": "^7.1.0", "npm-package-json-lint-config-default": "^6.0.0", "prettier": "^3.0.3", "prettier-plugin-curly": "^0.1.3", "prettier-plugin-packagejson": "^2.4.6", "release-it": "^17.0.0", "should-semantic-release": "^0.2.1", "tsup": "^7.2.0", "typescript": "^5.2.2", "yaml-eslint-parser": "^1.2.2"}, "peerDependencies": {"babel-plugin-macros": "^3.1.0"}, "peerDependenciesMeta": {"babel-plugin-macros": {"optional": true}}, "packageManager": "pnpm@8.7.0"}