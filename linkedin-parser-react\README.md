﻿# LinkedIn Parser - React 18.2.18 Implementation

一个基于 React 18.2.18 的 LinkedIn HTML 解析工具，将原有的 Python 解析器转换为现代化的 React 应用。

## 🚀 功能特性

- **多页面类型支持**: 搜索页面、公司信息页、人物资料页
- **自动页面检测**: 智能识别 LinkedIn 页面类型
- **登录状态支持**: 支持登录和未登录状态的页面解析
- **实时解析**: 即时解析和结果展示
- **数据导出**: 支持 JSON 格式数据导出
- **完整测试**: Jest + React Testing Library 测试套件
- **TypeScript**: 完整的类型安全支持

## 🛠️ 技术栈

- **React**: 18.2.18 (函数式组件 + Hooks)
- **TypeScript**: 类型安全的开发体验
- **Vite**: 快速的构建工具
- **Tailwind CSS**: 现代化的样式框架
- **Jest**: 单元测试框架
- **React Testing Library**: 组件测试工具

## 📦 安装和运行

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

应用将在 `http://localhost:3000` 启动

### 构建生产版本

```bash
npm run build
```

### 运行测试

```bash
# 运行所有测试
npm test

# 监听模式运行测试
npm run test:watch

# 生成测试覆盖率报告
npm run test:coverage
```

### 代码检查

```bash
# 检查代码规范
npm run lint

# 自动修复代码问题
npm run lint:fix
```

## 📁 项目结构

```
src/
├── components/          # React 组件
├── parsers/            # 解析器组件
│   ├── SearchPageParser.tsx
│   ├── CompanyInfoParser.tsx
│   ├── HumanInfoParser.tsx
│   └── LinkedInParser.tsx
├── types/              # TypeScript 类型定义
│   ├── parseModels.ts
│   └── index.ts
├── utils/              # 工具函数
│   ├── htmlParser.ts
│   ├── dataValidation.ts
│   └── index.ts
├── __tests__/          # 测试文件
└── App.tsx             # 主应用组件
```

## 🎯 使用方法

### 基本使用

1. **输入 HTML 内容**: 上传文件或直接粘贴 LinkedIn 页面的 HTML 内容
2. **选择页面类型**: 自动检测或手动选择页面类型
3. **配置解析选项**: 设置登录状态等参数
4. **开始解析**: 点击解析按钮获取结果
5. **查看结果**: 在结果页面查看解析数据
6. **导出数据**: 将解析结果导出为 JSON 文件

### 编程接口

```tsx
import { useLinkedInParser } from '@/parsers';

function MyComponent() {
  const { parseContent, isLoading, results } = useLinkedInParser();

  const handleParse = async () => {
    const result = await parseContent(htmlContent, 'auto_detect', {
      from_type: 'html',
      login_status: 'not_logged_in'
    });
    console.log(result);
  };

  return (
    <div>
      <button onClick={handleParse} disabled={isLoading}>
        {isLoading ? '解析中...' : '开始解析'}
      </button>
      {results && <div>解析完成！</div>}
    </div>
  );
}
```

## 🧪 测试

项目包含完整的测试套件：

- **单元测试**: 工具函数和解析逻辑测试
- **组件测试**: React 组件渲染和交互测试
- **集成测试**: 完整的解析流程测试

运行测试：

```bash
npm test
```

## 📊 解析器对比

| 功能 | Python 版本 | React 版本 |
|------|-------------|------------|
| 搜索页面解析 | ✅ | ✅ |
| 公司信息解析 | ✅ | ✅ |
| 人物信息解析 | ✅ | ✅ |
| JSON-LD 提取 | ✅ | ✅ |
| XPath 查询 | lxml | CSS Selectors + DOM API |
| 数据验证 | Pydantic | TypeScript + 自定义验证 |
| 用户界面 | ❌ | ✅ |
| 实时解析 | ❌ | ✅ |
| 数据导出 | ❌ | ✅ |

## 🔧 配置选项

### 解析配置

```typescript
interface ParserConfig {
  from_type: 'html' | 'html_all' | 'html_company' | 'html_people' | 'json';
  login_status: 'logged_in' | 'not_logged_in';
}
```

### 页面类型

- `auto_detect`: 自动检测页面类型
- `search`: LinkedIn 搜索结果页面
- `company_info`: LinkedIn 公司信息页面
- `human_info`: LinkedIn 人物资料页面

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
