{"name": "linkedin-parser-react", "version": "1.0.0", "description": "LinkedIn HTML Parser - React 18.2.18 Implementation", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix"}, "dependencies": {"react": "18.2.18", "react-dom": "18.2.18", "zustand": "^4.4.1", "clsx": "^2.0.0", "lucide-react": "^0.263.1"}, "devDependencies": {"@types/react": "^18.2.18", "@types/react-dom": "^18.2.7", "@types/jest": "^29.5.4", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.2", "@testing-library/user-event": "^14.4.3", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "@vitejs/plugin-react": "^4.0.4", "autoprefixer": "^10.4.15", "eslint": "^8.47.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jest": "^29.6.2", "jest-environment-jsdom": "^29.6.2", "postcss": "^8.4.28", "tailwindcss": "^3.3.3", "ts-jest": "^29.1.1", "typescript": "^5.1.6", "vite": "^4.4.9"}, "keywords": ["linkedin", "parser", "react", "typescript", "html-parsing"], "author": "LinkedIn Parser Team", "license": "MIT"}