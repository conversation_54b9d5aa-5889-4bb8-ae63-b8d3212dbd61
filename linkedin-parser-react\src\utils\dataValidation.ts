/**
 * Data Validation and Transformation Utilities
 * Replaces Python Pydantic validation with TypeScript runtime validation
 */

import { ParseError } from '@/types';

/**
 * Validation error class
 */
export class ValidationError extends Error {
  constructor(message: string, public field?: string, public value?: any) {
    super(message);
    this.name = 'ValidationError';
  }
}

/**
 * String length validator
 */
export function validateStringLength(value: string, maxLength: number, fieldName: string): string {
  if (value.length > maxLength) {
    throw new ValidationError(`${fieldName} exceeds maximum length of ${maxLength}`, fieldName, value);
  }
  return value;
}

/**
 * Non-negative integer validator
 */
export function validateNonNegativeInt(value: number, fieldName: string): number {
  if (value < 0) {
    throw new ValidationError(`${fieldName} must be non-negative`, fieldName, value);
  }
  return Math.floor(value);
}

/**
 * URL validator (basic)
 */
export function validateUrl(value: string, fieldName: string): string {
  if (value && !isValidUrl(value)) {
    console.warn(`${fieldName} may not be a valid URL: ${value}`);
  }
  return value;
}

/**
 * Basic URL validation
 */
function isValidUrl(url: string): boolean {
  try {
    new URL(url.startsWith('http') ? url : `https://${url}`);
    return true;
  } catch {
    return false;
  }
}

/**
 * Safe string extraction with default value
 */
export function safeString(value: any, defaultValue: string = ''): string {
  if (value === null || value === undefined) return defaultValue;
  return String(value).trim();
}

/**
 * Safe number extraction with default value
 */
export function safeNumber(value: any, defaultValue: number = 0): number {
  if (value === null || value === undefined) return defaultValue;
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
}

/**
 * Safe array extraction
 */
export function safeArray<T>(value: any, defaultValue: T[] = []): T[] {
  return Array.isArray(value) ? value : defaultValue;
}

/**
 * Parse JSON safely
 */
export function safeJsonParse<T>(jsonString: string, defaultValue: T): T {
  try {
    return JSON.parse(jsonString);
  } catch {
    return defaultValue;
  }
}

/**
 * Clean and normalize text content
 */
export function normalizeText(text: string): string {
  return text
    .replace(/\s+/g, ' ')  // Replace multiple whitespace with single space
    .replace(/[\r\n\t]/g, ' ')  // Replace line breaks and tabs with space
    .trim();
}

/**
 * Extract numbers from string (for follower counts, etc.)
 */
export function extractNumber(text: string): number {
  const match = text.match(/[\d,]+/);
  if (!match) return 0;
  
  const numberStr = match[0].replace(/,/g, '');
  return parseInt(numberStr, 10) || 0;
}

/**
 * Convert relative time to absolute date (basic implementation)
 */
export function parseRelativeTime(timeText: string): string {
  const now = new Date();
  const lowerText = timeText.toLowerCase();
  
  if (lowerText.includes('hour')) {
    const hours = extractNumber(timeText);
    now.setHours(now.getHours() - hours);
  } else if (lowerText.includes('day')) {
    const days = extractNumber(timeText);
    now.setDate(now.getDate() - days);
  } else if (lowerText.includes('week')) {
    const weeks = extractNumber(timeText);
    now.setDate(now.getDate() - (weeks * 7));
  } else if (lowerText.includes('month')) {
    const months = extractNumber(timeText);
    now.setMonth(now.getMonth() - months);
  } else if (lowerText.includes('year')) {
    const years = extractNumber(timeText);
    now.setFullYear(now.getFullYear() - years);
  }
  
  return now.toISOString().split('T')[0]; // Return YYYY-MM-DD format
}

/**
 * Validate and clean LinkedIn ID
 */
export function validateLinkedInId(id: string): string {
  const cleaned = id.replace(/[^a-zA-Z0-9\-_]/g, '');
  return validateStringLength(cleaned, 50, 'LinkedIn ID');
}

/**
 * Validate and clean LinkedIn URL
 */
export function validateLinkedInUrl(url: string): string {
  const cleaned = url.split('?')[0].trim(); // Remove query parameters
  return validateStringLength(cleaned, 500, 'LinkedIn URL');
}

/**
 * Create a validation result wrapper
 */
export interface ValidationResult<T> {
  success: boolean;
  data?: T;
  errors: ParseError[];
}

/**
 * Validate data with error collection
 */
export function validateWithErrors<T>(
  validationFn: () => T,
  fieldName: string
): ValidationResult<T> {
  try {
    const data = validationFn();
    return {
      success: true,
      data,
      errors: []
    };
  } catch (error) {
    const parseError: ParseError = {
      code: 'VALIDATION_ERROR',
      message: error instanceof Error ? error.message : 'Unknown validation error',
      details: { field: fieldName, error }
    };
    
    return {
      success: false,
      errors: [parseError]
    };
  }
}

/**
 * Batch validation for multiple fields
 */
export function batchValidate<T extends Record<string, any>>(
  data: T,
  validators: Record<keyof T, (value: any) => any>
): ValidationResult<T> {
  const errors: ParseError[] = [];
  const validatedData: Partial<T> = {};
  
  for (const [field, validator] of Object.entries(validators)) {
    const result = validateWithErrors(() => validator(data[field]), field);
    
    if (result.success && result.data !== undefined) {
      validatedData[field as keyof T] = result.data;
    } else {
      errors.push(...result.errors);
    }
  }
  
  return {
    success: errors.length === 0,
    data: errors.length === 0 ? validatedData as T : undefined,
    errors
  };
}
