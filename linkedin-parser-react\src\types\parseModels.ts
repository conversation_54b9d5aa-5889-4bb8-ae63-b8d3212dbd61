/**
 * LinkedIn Parser Data Models
 * Converted from Python Pydantic models to TypeScript interfaces
 */

// Base types for validation
export type LinkedInId = string; // max 50 chars
export type LinkedInUrl = string; // max 500 chars
export type PersonName = string; // max 300 chars
export type CompanyName = string; // max 300 chars

/**
 * 搜索综合页 人物 关注情况视图 解析模型
 */
export interface ParseSearchAllPeopleFollowee {
  linkedin_id?: string; // 领英人物 ID (max 50)
  follower_count?: number; // 领英人物 关注者数量 (>= 0)
}

/**
 * 搜索综合页 公司 关注情况视图 解析模型
 */
export interface ParseSearchAllCompanyFollowee {
  job_company_linkedin_id?: string; // 领英公司 ID (max 50)
  follower_count?: number; // 领英公司 关注者数量 (>= 0)
}

/**
 * 搜索综合页 人物 解析模型
 */
export interface ParseSearchAllPeople {
  linkedin_id?: string; // 领英人物 ID (max 50)
  linkedin_username?: string; // 领英人物 链接ID (max 100)
  linkedin_url?: string; // 领英人物 链接 (max 500)
  full_name?: string; // 领英人物 名称 (max 300)
  person_logos?: string; // 领英人物 头像列表json
  person_profiles?: string; // 领英人物 简介
  region?: string; // 领英人物 地区 (max 300)
  lid?: string; // 领英人物ID (max 30)
}

/**
 * 搜索综合页 公司 解析模型
 */
export interface ParseSearchAllCompany {
  job_company_id?: string; // 领英公司 链接ID (max 100)
  job_company_linkedin_id?: string; // 领英公司 ID (max 50)
  job_company_linkedin_url?: string; // 领英公司 链接 (max 500)
  job_company_name?: string; // 领英公司 名称 (max 300)
  company_logos?: string; // 领英公司 头像列表json
  company_profiles?: string; // 领英公司 简介
  job_search_url?: string; // 领英公司 职位搜索链接 (max 500)
  job_num?: string; // 领英公司 职位数量 (max 10)
  followers?: string; // 领英公司 关注者数量 (max 10)
}

/**
 * 搜索综合页 帖子 解析模型
 */
export interface ParseSearchAllPost {
  post_id?: string; // 领英帖子 ID (max 50)
  post_url?: string; // 领英帖子 链接 (max 500)
  post_content?: string; // 领英帖子 内容
  post_time?: string; // 领英帖子 发布时间 (max 50)
  people_model?: ParseSearchAllPeople; // 关联的人物模型
  company_model?: ParseSearchAllCompany; // 关联的公司模型
}

/**
 * 搜索综合页 帖子 统计视图 解析模型
 */
export interface ParseSearchAllPostCount {
  post_id?: string; // 领英帖子 ID (max 50)
  like_num?: number; // 领英帖子 点赞数量 (>= 0)
  comments_num?: number; // 领英帖子 评论数量 (>= 0)
  shares_num?: number; // 领英帖子 分享数量 (>= 0)
}

/**
 * 人物详情页 工作经历 解析模型
 */
export interface ParseHumanWorkExperience {
  job_title?: string; // 职位名称 (max 300)
  company_name?: string; // 公司名称 (max 300)
  company_url?: string; // 公司链接 (max 500)
  start_date?: string; // 开始时间 (max 50)
  end_date?: string; // 结束时间 (max 50)
  location?: string; // 工作地点 (max 300)
  description?: string; // 工作描述
}

/**
 * 人物详情页 教育经历 解析模型
 */
export interface ParseHumanEducationExperience {
  school_name?: string; // 学校名称 (max 300)
  school_url?: string; // 学校链接 (max 500)
  degree?: string; // 学位 (max 200)
  field_of_study?: string; // 专业 (max 200)
  start_date?: string; // 开始时间 (max 50)
  end_date?: string; // 结束时间 (max 50)
  description?: string; // 教育描述
}

/**
 * 人物详情页 语言能力 解析模型
 */
export interface ParseHumanLanguageAbility {
  language?: string; // 语言名称 (max 100)
  proficiency?: string; // 熟练程度 (max 100)
}

/**
 * 解析结果汇总接口
 */
export interface ParseResult {
  company_models: Record<string, ParseSearchAllCompany>;
  people_models: Record<string, ParseSearchAllPeople>;
  post_models: Record<string, ParseSearchAllPost>;
  company_followee_models: Record<string, ParseSearchAllCompanyFollowee>;
  people_followee_models: Record<string, ParseSearchAllPeopleFollowee>;
  post_count_models: Record<string, ParseSearchAllPostCount>;
}

/**
 * 解析器配置接口
 */
export interface ParserConfig {
  from_type: 'html' | 'html_all' | 'html_company' | 'html_people' | 'json';
  login_status: 'logged_in' | 'not_logged_in';
}

/**
 * 解析错误接口
 */
export interface ParseError {
  code: string;
  message: string;
  details?: any;
}
