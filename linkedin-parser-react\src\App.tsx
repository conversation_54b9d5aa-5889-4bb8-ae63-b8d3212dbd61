﻿/**
 * LinkedIn Parser - Main Application Component
 * React 18.2.18 Implementation
 */

import React, { useState, useCallback } from 'react';
import { FileText, Upload, Download, Settings, Info } from 'lucide-react';
import { LinkedInParser, useLinkedInParser, PageType } from '@/parsers';
import { ParserConfig, ParseError } from '@/types';
import { downloadAsFile } from '@/utils';

interface TabConfig {
  id: string;
  label: string;
  icon: React.ReactNode;
}

const tabs: TabConfig[] = [
  { id: 'input', label: '输入', icon: <Upload className="w-4 h-4" /> },
  { id: 'results', label: '结果', icon: <FileText className="w-4 h-4" /> },
  { id: 'settings', label: '设置', icon: <Settings className="w-4 h-4" /> },
  { id: 'about', label: '关于', icon: <Info className="w-4 h-4" /> }
];

function App() {
  const [activeTab, setActiveTab] = useState('input');
  const [htmlContent, setHtmlContent] = useState('');
  const [pageType, setPageType] = useState<PageType>('auto_detect');
  const [config, setConfig] = useState<ParserConfig>({
    from_type: 'html',
    login_status: 'not_logged_in'
  });
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const { parseContent, isLoading, stats } = useLinkedInParser();

  const handleParse = useCallback(async () => {
    if (!htmlContent.trim()) {
      setError('请输入 HTML 内容');
      return;
    }

    try {
      setError(null);
      const result = await parseContent(htmlContent, pageType, config);
      setResults(result);
      setActiveTab('results');
    } catch (err) {
      setError(err instanceof Error ? err.message : '解析失败');
    }
  }, [htmlContent, pageType, config, parseContent]);

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setHtmlContent(content);
      };
      reader.readAsText(file);
    }
  }, []);

  const handleExport = useCallback(() => {
    if (results) {
      const exportData = {
        timestamp: new Date().toISOString(),
        config,
        stats,
        results
      };
      downloadAsFile(
        JSON.stringify(exportData, null, 2),
        `linkedin-parse-${Date.now()}.json`,
        'application/json'
      );
    }
  }, [results, config, stats]);

  const renderInputTab = () => (
    <div className="space-y-6">
      <div className="card">
        <h2 className="text-xl font-semibold mb-4">HTML 内容输入</h2>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">上传 HTML 文件</label>
            <input
              type="file"
              accept=".html,.htm"
              onChange={handleFileUpload}
              className="input-field"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">或直接粘贴 HTML 内容</label>
            <textarea
              value={htmlContent}
              onChange={(e) => setHtmlContent(e.target.value)}
              placeholder="粘贴 LinkedIn 页面的 HTML 内容..."
              className="input-field h-64 font-mono text-sm"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">页面类型</label>
              <select
                value={pageType}
                onChange={(e) => setPageType(e.target.value as PageType)}
                className="input-field"
              >
                <option value="auto_detect">自动检测</option>
                <option value="search">搜索页面</option>
                <option value="company_info">公司信息页</option>
                <option value="human_info">人物信息页</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">登录状态</label>
              <select
                value={config.login_status}
                onChange={(e) => setConfig(prev => ({
                  ...prev,
                  login_status: e.target.value as 'logged_in' | 'not_logged_in'
                }))}
                className="input-field"
              >
                <option value="not_logged_in">未登录</option>
                <option value="logged_in">已登录</option>
              </select>
            </div>
          </div>

          <button
            onClick={handleParse}
            disabled={isLoading || !htmlContent.trim()}
            className="btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? '解析中...' : '开始解析'}
          </button>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderResultsTab = () => {
    if (!results) {
      return (
        <div className="card text-center py-12">
          <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">暂无解析结果</h3>
          <p className="text-gray-500">请先在输入页面解析 HTML 内容</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        <div className="card">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">解析结果</h2>
            <button
              onClick={handleExport}
              className="btn-secondary flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              导出结果
            </button>
          </div>

          {stats && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <h3 className="font-medium text-blue-800 mb-2">解析统计</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-blue-600">页面类型:</span>
                  <span className="ml-2 font-medium">{stats.pageType}</span>
                </div>
                <div>
                  <span className="text-blue-600">总项目:</span>
                  <span className="ml-2 font-medium">{stats.totalItems}</span>
                </div>
                <div>
                  <span className="text-blue-600">解析时间:</span>
                  <span className="ml-2 font-medium">
                    {new Date(stats.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                <div>
                  <span className="text-blue-600">状态:</span>
                  <span className="ml-2 font-medium text-green-600">成功</span>
                </div>
              </div>
            </div>
          )}

          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium mb-2">详细数据</h3>
            <pre className="text-xs overflow-auto max-h-96 bg-white p-3 rounded border">
              {JSON.stringify(results, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    );
  };

  const renderSettingsTab = () => (
    <div className="space-y-6">
      <div className="card">
        <h2 className="text-xl font-semibold mb-4">解析设置</h2>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">解析模式</label>
            <select
              value={config.from_type}
              onChange={(e) => setConfig(prev => ({
                ...prev,
                from_type: e.target.value as 'html' | 'html_all' | 'html_company' | 'html_people' | 'json'
              }))}
              className="input-field"
            >
              <option value="html">HTML (推荐)</option>
              <option value="html_all">HTML 全部</option>
              <option value="html_company">HTML 公司</option>
              <option value="html_people">HTML 人物</option>
              <option value="json">JSON</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">默认登录状态</label>
            <select
              value={config.login_status}
              onChange={(e) => setConfig(prev => ({
                ...prev,
                login_status: e.target.value as 'logged_in' | 'not_logged_in'
              }))}
              className="input-field"
            >
              <option value="not_logged_in">未登录</option>
              <option value="logged_in">已登录</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAboutTab = () => (
    <div className="space-y-6">
      <div className="card">
        <h2 className="text-xl font-semibold mb-4">关于 LinkedIn Parser</h2>

        <div className="space-y-4">
          <div>
            <h3 className="font-medium mb-2">项目信息</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 版本: 1.0.0</li>
              <li>• 技术栈: React 18.2.18 + TypeScript</li>
              <li>• 构建工具: Vite</li>
              <li>• 样式: Tailwind CSS</li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium mb-2">支持的页面类型</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• LinkedIn 搜索结果页面</li>
              <li>• LinkedIn 公司信息页面</li>
              <li>• LinkedIn 人物资料页面</li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium mb-2">功能特性</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 自动检测页面类型</li>
              <li>• 支持登录和未登录状态</li>
              <li>• 实时解析和结果展示</li>
              <li>• 数据导出功能</li>
              <li>• 完整的 TypeScript 支持</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'input':
        return renderInputTab();
      case 'results':
        return renderResultsTab();
      case 'settings':
        return renderSettingsTab();
      case 'about':
        return renderAboutTab();
      default:
        return renderInputTab();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <FileText className="w-8 h-8 text-linkedin-blue mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">
                LinkedIn Parser
              </h1>
            </div>
            <div className="text-sm text-gray-500">
              React 18.2.18 实现
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-linkedin-blue text-linkedin-blue'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon}
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderTabContent()}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-sm text-gray-500">
            LinkedIn Parser - 基于 React 18.2.18 的 HTML 解析工具
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;
